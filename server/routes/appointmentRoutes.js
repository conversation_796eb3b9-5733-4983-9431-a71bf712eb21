import express from 'express';
import {
  getAppointments,
  getAppointment,
  createAppointment,
  updateAppointment,
  deleteAppointment,
  getDoctorSchedule,
  getAvailableSlots
} from '../controllers/appointmentController.js';
import { protect } from '../controllers/authController.js';

const router = express.Router();

// Apply authentication to all routes
router.use(protect);

// Routes
router.route('/')
  .get(getAppointments)
  .post(createAppointment);

router.get('/doctor/:doctorId/schedule', getDoctorSchedule);
router.get('/available-slots', getAvailableSlots);

router.route('/:id')
  .get(getAppointment)
  .put(updateAppointment)
  .delete(deleteAppointment);

export default router;
