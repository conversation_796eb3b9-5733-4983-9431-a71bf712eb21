import express from 'express';
import {
  getAllUsers,
  createUser,
  updateUser,
  deleteUser,
  resetUserPassword,
  toggleUserStatus,
  getUserStats,
  getAllRoles,
  createRole,
  updateRole,
  deleteRole,
  getAuditLogs,
  createAuditLog,
  getSystemStats,
  getAvailablePermissions,
  getUserPermissions,
  checkUserPermission
} from '../controllers/adminController.js';
import { protect, restrictTo } from '../controllers/authController.js';

const router = express.Router();

// Temporary bypass for development - remove in production
// Apply authentication to all routes
// router.use(protect);

// Apply admin restriction to all routes
// router.use(restrictTo('Administrator'));

// Temporary middleware to simulate admin user
router.use((req, res, next) => {
  // Simulate an authenticated admin user for development
  req.user = {
    id: 'admin-user-id',
    role: { name: 'Administrator' },
    permissions: ['ALL']
  };
  next();
});

// User management routes
router.route('/users')
  .get(getAllUsers)
  .post(createUser);

router.route('/users/:id')
  .put(updateUser)
  .delete(deleteUser);

router.put('/users/:id/reset-password', resetUserPassword);
router.put('/users/:id/toggle-status', toggleUserStatus);
router.get('/users/stats', getUserStats);

// Role management routes
router.route('/roles')
  .get(getAllRoles)
  .post(createRole);

router.route('/roles/:id')
  .put(updateRole)
  .delete(deleteRole);

// Audit log routes
router.route('/audit-logs')
  .get(getAuditLogs)
  .post(createAuditLog);

// System statistics
router.get('/system-stats', getSystemStats);

// ===== PERMISSION MANAGEMENT =====
// Get available permissions
router.get('/permissions', getAvailablePermissions);

// Get user permissions
router.get('/users/:id/permissions', getUserPermissions);

// Check specific permission for user
router.get('/users/:id/permissions/:permission', checkUserPermission);

export default router;
