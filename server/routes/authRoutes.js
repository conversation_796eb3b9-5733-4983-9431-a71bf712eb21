import express from 'express';
import { login, register, verifyToken, protect, getProfile, updateProfile, changePassword } from '../controllers/authController.js';

const router = express.Router();

// Public routes
router.post('/login', login);
router.post('/register', register);

// Protected routes
router.get('/verify', protect, verifyToken);
router.route('/profile')
  .get(protect, getProfile)
  .put(protect, updateProfile);
router.put('/change-password', protect, changePassword);

export default router;
