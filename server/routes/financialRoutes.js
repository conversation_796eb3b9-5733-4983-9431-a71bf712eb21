import express from 'express';
import {
  getBills,
  getBill,
  createBill,
  updateBill,
  addPayment,
  getFinancialStats
} from '../controllers/financialController.js';
import { protect } from '../controllers/authController.js';

const router = express.Router();

// Apply authentication to all routes
router.use(protect);

// Bill routes
router.route('/bills')
  .get(getBills)
  .post(createBill);

router.route('/bills/:id')
  .get(getBill)
  .put(updateBill);

// Payment routes
router.post('/bills/:id/payments', addPayment);

// Statistics
router.get('/stats', getFinancialStats);

export default router;
