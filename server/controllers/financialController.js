import mongoose from 'mongoose';

// Bill schema
const BillSchema = new mongoose.Schema({
  billId: {
    type: String,
    required: true,
    unique: true,
    default: function() {
      return 'BILL' + Date.now().toString().slice(-6);
    }
  },
  patient: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Patient',
    required: true,
    index: true
  },
  appointment: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Appointment'
  },
  billDate: {
    type: Date,
    required: true,
    default: Date.now
  },
  dueDate: {
    type: Date,
    required: true,
    default: function() {
      return new Date(Date.now() + 30 * 24 * 60 * 60 * 1000); // 30 days from now
    }
  },
  items: [{
    description: { type: String, required: true },
    quantity: { type: Number, required: true, min: 0 },
    unitPrice: { type: Number, required: true, min: 0 },
    totalPrice: { type: Number, required: true, min: 0 },
    category: {
      type: String,
      required: true,
      enum: ['Consultation', 'Procedure', 'Medication', 'Laboratory', 'Imaging', 'Room Charges', 'Other']
    }
  }],
  subtotal: {
    type: Number,
    required: true,
    min: 0
  },
  tax: {
    type: Number,
    default: 0,
    min: 0
  },
  discount: {
    type: Number,
    default: 0,
    min: 0
  },
  totalAmount: {
    type: Number,
    required: true,
    min: 0
  },
  paidAmount: {
    type: Number,
    default: 0,
    min: 0
  },
  balanceAmount: {
    type: Number,
    required: true,
    min: 0
  },
  paymentStatus: {
    type: String,
    required: true,
    enum: ['Pending', 'Partial', 'Paid', 'Overdue', 'Cancelled'],
    default: 'Pending',
    index: true
  },
  paymentMethod: {
    type: String,
    enum: ['Cash', 'Credit Card', 'Debit Card', 'Insurance', 'Bank Transfer', 'Check']
  },
  insurance: {
    provider: String,
    policyNumber: String,
    claimNumber: String,
    approvedAmount: { type: Number, min: 0 },
    status: {
      type: String,
      enum: ['Pending', 'Approved', 'Rejected', 'Partial'],
      default: 'Pending'
    }
  },
  payments: [{
    date: { type: Date, required: true },
    amount: { type: Number, required: true, min: 0 },
    method: { type: String, required: true },
    reference: String,
    receivedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    }
  }],
  notes: {
    type: String,
    trim: true
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// Indexes
BillSchema.index({ billId: 1 });
BillSchema.index({ patient: 1 });
BillSchema.index({ billDate: 1 });
BillSchema.index({ paymentStatus: 1 });
BillSchema.index({ dueDate: 1 });

// Pre-save middleware to calculate totals
BillSchema.pre('save', function(next) {
  // Calculate subtotal
  this.subtotal = this.items.reduce((sum, item) => sum + item.totalPrice, 0);
  
  // Calculate total amount
  this.totalAmount = this.subtotal + this.tax - this.discount;
  
  // Calculate balance amount
  this.balanceAmount = this.totalAmount - this.paidAmount;
  
  // Update payment status based on balance
  if (this.balanceAmount <= 0) {
    this.paymentStatus = 'Paid';
  } else if (this.paidAmount > 0) {
    this.paymentStatus = 'Partial';
  } else if (new Date() > this.dueDate) {
    this.paymentStatus = 'Overdue';
  } else {
    this.paymentStatus = 'Pending';
  }
  
  next();
});

// Virtual for overdue status
BillSchema.virtual('isOverdue').get(function() {
  return this.paymentStatus !== 'Paid' && new Date() > this.dueDate;
});

// Get existing model or create new one
let Bill;
try {
  Bill = mongoose.model('Bill');
} catch (error) {
  Bill = mongoose.model('Bill', BillSchema);
}

// @desc    Get all bills with pagination and filtering
// @route   GET /api/financial/bills
// @access  Private
export const getBills = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // Build filter object
    const filter = {};
    
    if (req.query.status) {
      filter.paymentStatus = req.query.status;
    }
    
    if (req.query.patient) {
      filter.patient = req.query.patient;
    }
    
    if (req.query.dateFrom || req.query.dateTo) {
      filter.billDate = {};
      if (req.query.dateFrom) {
        filter.billDate.$gte = new Date(req.query.dateFrom);
      }
      if (req.query.dateTo) {
        filter.billDate.$lte = new Date(req.query.dateTo);
      }
    }

    const bills = await Bill.find(filter)
      .populate('patient', 'firstName lastName patientId phone email')
      .populate('appointment', 'appointmentId appointmentDate')
      .populate('createdBy', 'firstName lastName')
      .populate('payments.receivedBy', 'firstName lastName')
      .sort({ billDate: -1 })
      .skip(skip)
      .limit(limit);

    const total = await Bill.countDocuments(filter);

    res.status(200).json({
      success: true,
      data: bills,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Get bills error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching bills'
    });
  }
};

// @desc    Get single bill
// @route   GET /api/financial/bills/:id
// @access  Private
export const getBill = async (req, res) => {
  try {
    const bill = await Bill.findById(req.params.id)
      .populate('patient', 'firstName lastName patientId phone email address')
      .populate('appointment', 'appointmentId appointmentDate appointmentTime')
      .populate('createdBy', 'firstName lastName email')
      .populate('updatedBy', 'firstName lastName email')
      .populate('payments.receivedBy', 'firstName lastName');

    if (!bill) {
      return res.status(404).json({
        success: false,
        error: 'Bill not found'
      });
    }

    res.status(200).json({
      success: true,
      data: bill
    });
  } catch (error) {
    console.error('Get bill error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching bill'
    });
  }
};

// @desc    Create new bill
// @route   POST /api/financial/bills
// @access  Private
export const createBill = async (req, res) => {
  try {
    req.body.createdBy = req.user._id;

    const bill = await Bill.create(req.body);

    const populatedBill = await Bill.findById(bill._id)
      .populate('patient', 'firstName lastName patientId phone email')
      .populate('appointment', 'appointmentId appointmentDate')
      .populate('createdBy', 'firstName lastName');

    res.status(201).json({
      success: true,
      data: populatedBill
    });
  } catch (error) {
    console.error('Create bill error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while creating bill'
    });
  }
};

// @desc    Update bill
// @route   PUT /api/financial/bills/:id
// @access  Private
export const updateBill = async (req, res) => {
  try {
    const bill = await Bill.findById(req.params.id);

    if (!bill) {
      return res.status(404).json({
        success: false,
        error: 'Bill not found'
      });
    }

    req.body.updatedBy = req.user._id;

    const updatedBill = await Bill.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true, runValidators: true }
    )
      .populate('patient', 'firstName lastName patientId phone email')
      .populate('appointment', 'appointmentId appointmentDate')
      .populate('createdBy', 'firstName lastName')
      .populate('updatedBy', 'firstName lastName')
      .populate('payments.receivedBy', 'firstName lastName');

    res.status(200).json({
      success: true,
      data: updatedBill
    });
  } catch (error) {
    console.error('Update bill error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while updating bill'
    });
  }
};

// @desc    Add payment to bill
// @route   POST /api/financial/bills/:id/payments
// @access  Private
export const addPayment = async (req, res) => {
  try {
    const bill = await Bill.findById(req.params.id);

    if (!bill) {
      return res.status(404).json({
        success: false,
        error: 'Bill not found'
      });
    }

    const payment = {
      ...req.body,
      receivedBy: req.user._id,
      date: new Date()
    };

    bill.payments.push(payment);
    bill.paidAmount += payment.amount;
    bill.updatedBy = req.user._id;

    await bill.save();

    const updatedBill = await Bill.findById(bill._id)
      .populate('patient', 'firstName lastName patientId')
      .populate('payments.receivedBy', 'firstName lastName');

    res.status(200).json({
      success: true,
      data: updatedBill
    });
  } catch (error) {
    console.error('Add payment error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while adding payment'
    });
  }
};

// @desc    Get financial statistics
// @route   GET /api/financial/stats
// @access  Private
export const getFinancialStats = async (req, res) => {
  try {
    const today = new Date();
    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    const endOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);

    const stats = await Promise.all([
      // Total revenue this month
      Bill.aggregate([
        {
          $match: {
            billDate: { $gte: startOfMonth, $lte: endOfMonth },
            paymentStatus: { $ne: 'Cancelled' }
          }
        },
        {
          $group: {
            _id: null,
            totalRevenue: { $sum: '$totalAmount' }
          }
        }
      ]),
      // Outstanding amount
      Bill.aggregate([
        {
          $match: {
            paymentStatus: { $in: ['Pending', 'Partial', 'Overdue'] }
          }
        },
        {
          $group: {
            _id: null,
            outstandingAmount: { $sum: '$balanceAmount' }
          }
        }
      ]),
      // Overdue bills count
      Bill.countDocuments({
        paymentStatus: 'Overdue'
      }),
      // Paid bills this month
      Bill.countDocuments({
        billDate: { $gte: startOfMonth, $lte: endOfMonth },
        paymentStatus: 'Paid'
      })
    ]);

    res.status(200).json({
      success: true,
      data: {
        monthlyRevenue: stats[0][0]?.totalRevenue || 0,
        outstandingAmount: stats[1][0]?.outstandingAmount || 0,
        overdueBills: stats[2],
        paidBillsThisMonth: stats[3]
      }
    });
  } catch (error) {
    console.error('Get financial stats error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching financial statistics'
    });
  }
};
