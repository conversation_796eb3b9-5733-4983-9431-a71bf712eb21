import mongoose from 'mongoose';
import Bill from '../models/Bill.js';


// @desc    Get all bills with pagination and filtering
// @route   GET /api/financial/bills
// @access  Private
export const getBills = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // Build filter object
    const filter = {};
    
    if (req.query.status) {
      filter.paymentStatus = req.query.status;
    }
    
    if (req.query.patient) {
      filter.patient = req.query.patient;
    }
    
    if (req.query.dateFrom || req.query.dateTo) {
      filter.billDate = {};
      if (req.query.dateFrom) {
        filter.billDate.$gte = new Date(req.query.dateFrom);
      }
      if (req.query.dateTo) {
        filter.billDate.$lte = new Date(req.query.dateTo);
      }
    }

    const bills = await Bill.find(filter)
      .populate('patient', 'firstName lastName patientId phone email')
      .populate('appointment', 'appointmentId appointmentDate')
      .populate('createdBy', 'firstName lastName')
      .populate('payments.receivedBy', 'firstName lastName')
      .sort({ billDate: -1 })
      .skip(skip)
      .limit(limit);

    const total = await Bill.countDocuments(filter);

    res.status(200).json({
      success: true,
      data: bills,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Get bills error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching bills'
    });
  }
};

// @desc    Get single bill
// @route   GET /api/financial/bills/:id
// @access  Private
export const getBill = async (req, res) => {
  try {
    const bill = await Bill.findById(req.params.id)
      .populate('patient', 'firstName lastName patientId phone email address')
      .populate('appointment', 'appointmentId appointmentDate appointmentTime')
      .populate('createdBy', 'firstName lastName email')
      .populate('updatedBy', 'firstName lastName email')
      .populate('payments.receivedBy', 'firstName lastName');

    if (!bill) {
      return res.status(404).json({
        success: false,
        error: 'Bill not found'
      });
    }

    res.status(200).json({
      success: true,
      data: bill
    });
  } catch (error) {
    console.error('Get bill error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching bill'
    });
  }
};

// @desc    Create new bill
// @route   POST /api/financial/bills
// @access  Private
export const createBill = async (req, res) => {
  try {
    const {
      patient,
      appointment,
      items,
      subtotal,
      tax = 0,
      discount = 0,
      totalAmount,
      notes,
      dueDate
    } = req.body;

    // Validation
    if (!patient || !items || !Array.isArray(items) || items.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Patient and items are required'
      });
    }

    // Calculate totals if not provided
    const calculatedSubtotal = subtotal || items.reduce((sum, item) => sum + (item.totalPrice || 0), 0);
    const calculatedTotal = totalAmount || (calculatedSubtotal + tax - discount);

    // Generate bill ID
    const billId = `BILL${Date.now().toString().slice(-6)}`;

    // Set due date (30 days from now if not provided)
    const finalDueDate = dueDate ? new Date(dueDate) : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);

    const billData = {
      billId,
      patient,
      appointment,
      items,
      subtotal: calculatedSubtotal,
      tax,
      discount,
      totalAmount: calculatedTotal,
      paidAmount: 0,
      balanceAmount: calculatedTotal, // Will be recalculated by pre-save hook
      dueDate: finalDueDate,
      notes,
      createdBy: req.user._id
    };

    const bill = await Bill.create(billData);

    const populatedBill = await Bill.findById(bill._id)
      .populate('patient', 'firstName lastName patientId phone email')
      .populate('appointment', 'appointmentId appointmentDate')
      .populate('createdBy', 'firstName lastName');

    res.status(201).json({
      success: true,
      data: populatedBill
    });
  } catch (error) {
    console.error('Create bill error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while creating bill'
    });
  }
};

// @desc    Update bill
// @route   PUT /api/financial/bills/:id
// @access  Private
export const updateBill = async (req, res) => {
  try {
    const bill = await Bill.findById(req.params.id);

    if (!bill) {
      return res.status(404).json({
        success: false,
        error: 'Bill not found'
      });
    }

    req.body.updatedBy = req.user._id;

    const updatedBill = await Bill.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true, runValidators: true }
    )
      .populate('patient', 'firstName lastName patientId phone email')
      .populate('appointment', 'appointmentId appointmentDate')
      .populate('createdBy', 'firstName lastName')
      .populate('updatedBy', 'firstName lastName')
      .populate('payments.receivedBy', 'firstName lastName');

    res.status(200).json({
      success: true,
      data: updatedBill
    });
  } catch (error) {
    console.error('Update bill error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while updating bill'
    });
  }
};

// @desc    Add payment to bill
// @route   POST /api/financial/bills/:id/payments
// @access  Private
export const addPayment = async (req, res) => {
  try {
    const bill = await Bill.findById(req.params.id);

    if (!bill) {
      return res.status(404).json({
        success: false,
        error: 'Bill not found'
      });
    }

    const payment = {
      ...req.body,
      receivedBy: req.user._id,
      date: new Date()
    };

    bill.payments.push(payment);
    bill.paidAmount += payment.amount;
    bill.updatedBy = req.user._id;

    await bill.save();

    const updatedBill = await Bill.findById(bill._id)
      .populate('patient', 'firstName lastName patientId')
      .populate('payments.receivedBy', 'firstName lastName');

    res.status(200).json({
      success: true,
      data: updatedBill
    });
  } catch (error) {
    console.error('Add payment error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while adding payment'
    });
  }
};

// @desc    Get financial statistics
// @route   GET /api/financial/stats
// @access  Private
export const getFinancialStats = async (req, res) => {
  try {
    const today = new Date();
    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    const endOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);

    const stats = await Promise.all([
      // Total revenue this month
      Bill.aggregate([
        {
          $match: {
            billDate: { $gte: startOfMonth, $lte: endOfMonth },
            paymentStatus: { $ne: 'Cancelled' }
          }
        },
        {
          $group: {
            _id: null,
            totalRevenue: { $sum: '$totalAmount' }
          }
        }
      ]),
      // Outstanding amount
      Bill.aggregate([
        {
          $match: {
            paymentStatus: { $in: ['Pending', 'Partial', 'Overdue'] }
          }
        },
        {
          $group: {
            _id: null,
            outstandingAmount: { $sum: '$balanceAmount' }
          }
        }
      ]),
      // Overdue bills count
      Bill.countDocuments({
        paymentStatus: 'Overdue'
      }),
      // Paid bills this month
      Bill.countDocuments({
        billDate: { $gte: startOfMonth, $lte: endOfMonth },
        paymentStatus: 'Paid'
      })
    ]);

    res.status(200).json({
      success: true,
      data: {
        monthlyRevenue: stats[0][0]?.totalRevenue || 0,
        outstandingAmount: stats[1][0]?.outstandingAmount || 0,
        overdueBills: stats[2],
        paidBillsThisMonth: stats[3]
      }
    });
  } catch (error) {
    console.error('Get financial stats error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching financial statistics'
    });
  }
};

// @desc    Get payment trends
// @route   GET /api/financial/payment-trends
// @access  Private
export const getPaymentTrends = async (req, res) => {
  try {
    const period = req.query.period || '30'; // days
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(period));

    const trends = await Bill.aggregate([
      {
        $match: {
          billDate: { $gte: startDate },
          paymentStatus: { $ne: 'Cancelled' }
        }
      },
      {
        $group: {
          _id: {
            $dateToString: { format: "%Y-%m-%d", date: "$billDate" }
          },
          totalAmount: { $sum: "$totalAmount" },
          paidAmount: { $sum: "$paidAmount" },
          billCount: { $sum: 1 }
        }
      },
      {
        $sort: { _id: 1 }
      }
    ]);

    res.status(200).json({
      success: true,
      data: trends
    });
  } catch (error) {
    console.error('Get payment trends error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching payment trends'
    });
  }
};

// @desc    Get overdue bills
// @route   GET /api/financial/overdue-bills
// @access  Private
export const getOverdueBills = async (req, res) => {
  try {
    const overdueBills = await Bill.find({
      paymentStatus: 'Overdue'
    })
      .populate('patient', 'firstName lastName patientId phone email')
      .populate('appointment', 'appointmentId appointmentDate')
      .sort({ dueDate: 1 })
      .limit(50);

    res.status(200).json({
      success: true,
      data: overdueBills
    });
  } catch (error) {
    console.error('Get overdue bills error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching overdue bills'
    });
  }
};

// @desc    Get revenue analytics
// @route   GET /api/financial/revenue-analytics
// @access  Private
export const getRevenueAnalytics = async (req, res) => {
  try {
    const period = req.query.period || 'month';
    let startDate, groupFormat;

    switch (period) {
      case 'week':
        startDate = new Date();
        startDate.setDate(startDate.getDate() - 7);
        groupFormat = "%Y-%m-%d";
        break;
      case 'year':
        startDate = new Date();
        startDate.setFullYear(startDate.getFullYear() - 1);
        groupFormat = "%Y-%m";
        break;
      default: // month
        startDate = new Date();
        startDate.setMonth(startDate.getMonth() - 1);
        groupFormat = "%Y-%m-%d";
    }

    const analytics = await Bill.aggregate([
      {
        $match: {
          billDate: { $gte: startDate },
          paymentStatus: { $ne: 'Cancelled' }
        }
      },
      {
        $group: {
          _id: {
            $dateToString: { format: groupFormat, date: "$billDate" }
          },
          revenue: { $sum: "$totalAmount" },
          collected: { $sum: "$paidAmount" },
          outstanding: { $sum: "$balanceAmount" },
          billCount: { $sum: 1 }
        }
      },
      {
        $sort: { _id: 1 }
      }
    ]);

    res.status(200).json({
      success: true,
      data: analytics
    });
  } catch (error) {
    console.error('Get revenue analytics error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching revenue analytics'
    });
  }
};

// @desc    Get insurance claims
// @route   GET /api/financial/insurance-claims
// @access  Private
export const getInsuranceClaims = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const filter = {
      'insurance.provider': { $exists: true, $ne: null }
    };

    if (req.query.status) {
      filter['insurance.status'] = req.query.status;
    }

    const claims = await Bill.find(filter)
      .populate('patient', 'firstName lastName patientId phone email')
      .populate('appointment', 'appointmentId appointmentDate')
      .sort({ billDate: -1 })
      .skip(skip)
      .limit(limit);

    const total = await Bill.countDocuments(filter);

    res.status(200).json({
      success: true,
      data: claims,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Get insurance claims error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching insurance claims'
    });
  }
};
