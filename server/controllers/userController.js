import { Request, Response } from 'express';
import mongoose from 'mongoose';
import bcrypt from 'bcryptjs';

interface AuthRequest extends Request {
  user?: any;
}

// Get existing models
const getModels = () => {
  try {
    return {
      User: mongoose.model('User'),
      Role: mongoose.model('Role'),
      Permission: mongoose.model('Permission')
    };
  } catch (error) {
    console.error('Error getting models:', error);
    return {};
  }
};

// @desc    Get all users
// @route   GET /api/users
// @access  Private (Admin only)
export const getUsers = async (req: AuthRequest, res: Response) => {
  try {
    const models = getModels();
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const skip = (page - 1) * limit;

    // Build filter
    const filter: any = {};
    if (req.query.role) {
      filter.role = req.query.role;
    }
    if (req.query.department) {
      filter.department = req.query.department;
    }
    if (req.query.isActive !== undefined) {
      filter.isActive = req.query.isActive === 'true';
    }

    // Search functionality
    if (req.query.search) {
      const searchRegex = new RegExp(req.query.search as string, 'i');
      filter.$or = [
        { firstName: searchRegex },
        { lastName: searchRegex },
        { username: searchRegex },
        { email: searchRegex },
        { department: searchRegex }
      ];
    }

    const users = await models.User?.find(filter)
      .populate('role', 'name description level')
      .populate('permissions', 'module action resource description')
      .select('-password')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit) || [];

    const total = await models.User?.countDocuments(filter) || 0;

    res.status(200).json({
      success: true,
      data: users,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Get users error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching users'
    });
  }
};

// @desc    Get single user
// @route   GET /api/users/:id
// @access  Private
export const getUser = async (req: AuthRequest, res: Response) => {
  try {
    const models = getModels();
    const user = await models.User?.findById(req.params.id)
      .populate('role', 'name description level')
      .populate('permissions', 'module action resource description')
      .select('-password');

    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    res.status(200).json({
      success: true,
      data: user
    });
  } catch (error) {
    console.error('Get user error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching user'
    });
  }
};

// @desc    Create new user
// @route   POST /api/users
// @access  Private (Admin only)
export const createUser = async (req: AuthRequest, res: Response) => {
  try {
    const models = getModels();
    const { username, email, password, firstName, lastName, role, department } = req.body;

    // Check if user already exists
    const existingUser = await models.User?.findOne({
      $or: [{ email }, { username }]
    });

    if (existingUser) {
      return res.status(400).json({
        success: false,
        error: 'User with this email or username already exists'
      });
    }

    // Get role and its default permissions
    const userRole = await models.Role?.findById(role).populate('defaultPermissions');
    if (!userRole) {
      return res.status(400).json({
        success: false,
        error: 'Invalid role specified'
      });
    }

    // Create user
    const newUser = await models.User?.create({
      username,
      email,
      password,
      firstName,
      lastName,
      role,
      permissions: userRole.defaultPermissions.map((p: any) => p._id),
      department,
      isActive: true
    });

    // Return user without password
    const userResponse = await models.User?.findById(newUser._id)
      .populate('role', 'name description level')
      .populate('permissions', 'module action resource description')
      .select('-password');

    res.status(201).json({
      success: true,
      data: userResponse
    });
  } catch (error) {
    console.error('Create user error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while creating user'
    });
  }
};

// @desc    Update user
// @route   PUT /api/users/:id
// @access  Private (Admin only)
export const updateUser = async (req: AuthRequest, res: Response) => {
  try {
    const models = getModels();
    const { firstName, lastName, email, department, isActive, role } = req.body;

    const user = await models.User?.findById(req.params.id);
    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    // Check if email is being changed and if it's already taken
    if (email && email !== user.email) {
      const existingUser = await models.User?.findOne({ email });
      if (existingUser) {
        return res.status(400).json({
          success: false,
          error: 'Email already in use'
        });
      }
    }

    // Update user fields
    const updateData: any = {};
    if (firstName) updateData.firstName = firstName;
    if (lastName) updateData.lastName = lastName;
    if (email) updateData.email = email;
    if (department) updateData.department = department;
    if (isActive !== undefined) updateData.isActive = isActive;
    if (role) {
      updateData.role = role;
      // Update permissions based on new role
      const userRole = await models.Role?.findById(role).populate('defaultPermissions');
      if (userRole) {
        updateData.permissions = userRole.defaultPermissions.map((p: any) => p._id);
      }
    }

    const updatedUser = await models.User?.findByIdAndUpdate(
      req.params.id,
      updateData,
      { new: true }
    ).populate('role', 'name description level')
     .populate('permissions', 'module action resource description')
     .select('-password');

    res.status(200).json({
      success: true,
      data: updatedUser
    });
  } catch (error) {
    console.error('Update user error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while updating user'
    });
  }
};

// @desc    Delete user
// @route   DELETE /api/users/:id
// @access  Private (Admin only)
export const deleteUser = async (req: AuthRequest, res: Response) => {
  try {
    const models = getModels();

    // Prevent deletion of current user
    if (req.params.id === req.user._id.toString()) {
      return res.status(400).json({
        success: false,
        error: 'Cannot delete your own account'
      });
    }

    const user = await models.User?.findById(req.params.id);
    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    // Soft delete by setting isActive to false
    await models.User?.findByIdAndUpdate(req.params.id, { isActive: false });

    res.status(200).json({
      success: true,
      message: 'User deactivated successfully'
    });
  } catch (error) {
    console.error('Delete user error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while deleting user'
    });
  }
};

// @desc    Update user permissions
// @route   PUT /api/users/:id/permissions
// @access  Private (Admin only)
export const updateUserPermissions = async (req: AuthRequest, res: Response) => {
  try {
    const models = getModels();
    const { permissions } = req.body;

    const user = await models.User?.findById(req.params.id);
    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    // Validate permissions exist
    const validPermissions = await models.Permission?.find({
      _id: { $in: permissions }
    });

    if (validPermissions.length !== permissions.length) {
      return res.status(400).json({
        success: false,
        error: 'Some permissions are invalid'
      });
    }

    // Update user permissions
    const updatedUser = await models.User?.findByIdAndUpdate(
      req.params.id,
      { permissions },
      { new: true }
    ).populate('role', 'name description level')
     .populate('permissions', 'module action resource description')
     .select('-password');

    res.status(200).json({
      success: true,
      data: updatedUser
    });
  } catch (error) {
    console.error('Update user permissions error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while updating permissions'
    });
  }
};

// @desc    Get users by role
// @route   GET /api/users/role/:roleId
// @access  Private
export const getUsersByRole = async (req: AuthRequest, res: Response) => {
  try {
    const models = getModels();

    const users = await models.User?.find({
      role: req.params.roleId,
      isActive: true
    })
    .populate('role', 'name description level')
    .select('firstName lastName email department')
    .sort({ firstName: 1 }) || [];

    res.status(200).json({
      success: true,
      data: users
    });
  } catch (error) {
    console.error('Get users by role error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching users by role'
    });
  }
};

// @desc    Get all roles
// @route   GET /api/roles
// @access  Private
export const getRoles = async (req: AuthRequest, res: Response) => {
  try {
    const models = getModels();

    const roles = await models.Role?.find()
      .populate('defaultPermissions', 'module action resource description')
      .sort({ level: -1 }) || [];

    res.status(200).json({
      success: true,
      data: roles
    });
  } catch (error) {
    console.error('Get roles error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching roles'
    });
  }
};

// @desc    Get all permissions
// @route   GET /api/permissions
// @access  Private
export const getPermissions = async (req: AuthRequest, res: Response) => {
  try {
    const models = getModels();

    const permissions = await models.Permission?.find()
      .sort({ module: 1, action: 1 }) || [];

    res.status(200).json({
      success: true,
      data: permissions
    });
  } catch (error) {
    console.error('Get permissions error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching permissions'
    });
  }
};
