import bcrypt from 'bcryptjs';
import mongoose from 'mongoose';

// Import models directly
import Role from '../models/Role.js';
import AuditLog from '../models/AuditLog.js';

// Get existing models with better error handling
const getModels = () => {
  const models = {};

  try {
    models.User = mongoose.model('User');
  } catch (error) {
    console.warn('User model not available:', error.message);
  }

  // Use imported Role model
  models.Role = Role;

  // Use imported AuditLog model
  models.AuditLog = AuditLog;

  try {
    models.Patient = mongoose.model('Patient');
  } catch (error) {
    console.warn('Patient model not available:', error.message);
  }

  try {
    models.Appointment = mongoose.model('Appointment');
  } catch (error) {
    console.warn('Appointment model not available:', error.message);
  }

  return models;
};

// Helper function to generate employee ID
const generateEmployeeId = async () => {
  const models = getModels();
  if (!models.User) return 'EMP001';

  const lastUser = await models.User.findOne({}, {}, { sort: { 'createdAt': -1 } });
  if (!lastUser || !lastUser.employeeId) {
    return 'EMP001';
  }

  const lastNumber = parseInt(lastUser.employeeId.replace('EMP', ''));
  const nextNumber = lastNumber + 1;
  return `EMP${nextNumber.toString().padStart(3, '0')}`;
};

// Helper function to create default roles
const createDefaultRoles = async () => {
  const models = getModels();
  if (!models.Role) {
    console.warn('Role model not available, skipping default role creation');
    return;
  }

  const defaultRoles = [
    {
      name: 'Administrator',
      description: 'Full system access with all administrative privileges',
      permissions: [
        'USER_MANAGEMENT',
        'ROLE_MANAGEMENT',
        'PATIENT_MANAGEMENT',
        'APPOINTMENT_MANAGEMENT',
        'BILLING_MANAGEMENT',
        'REPORT_GENERATION',
        'FACILITY_MANAGEMENT',
        'LABORATORY_MANAGEMENT',
        'PHARMACY_MANAGEMENT',
        'SYSTEM_ADMINISTRATION',
        'AUDIT_LOG_ACCESS',
        'BACKUP_RESTORE',
        'NOTIFICATION_MANAGEMENT'
      ],
      isSystemRole: true,
      isActive: true
    },
    {
      name: 'Doctor',
      description: 'Medical staff with patient care and medical record access',
      permissions: [
        'PATIENT_MANAGEMENT',
        'APPOINTMENT_MANAGEMENT',
        'LABORATORY_MANAGEMENT',
        'PHARMACY_MANAGEMENT',
        'REPORT_GENERATION'
      ],
      isSystemRole: true,
      isActive: true
    },
    {
      name: 'Nurse',
      description: 'Nursing staff with patient care and medication management',
      permissions: [
        'PATIENT_MANAGEMENT',
        'PHARMACY_MANAGEMENT',
        'LABORATORY_MANAGEMENT'
      ],
      isSystemRole: true,
      isActive: true
    },
    {
      name: 'Receptionist',
      description: 'Front desk staff with appointment and basic patient management',
      permissions: [
        'APPOINTMENT_MANAGEMENT',
        'PATIENT_MANAGEMENT'
      ],
      isSystemRole: true,
      isActive: true
    }
  ];

  try {
    for (const roleData of defaultRoles) {
      const existingRole = await models.Role.findOne({ name: roleData.name });
      if (!existingRole) {
        await models.Role.create(roleData);
        console.log(`✅ Created default role: ${roleData.name}`);
      } else {
        console.log(`ℹ️  Role already exists: ${roleData.name}`);
      }
    }
  } catch (error) {
    console.error('Error creating default roles:', error.message);
  }
};

// Available permissions with descriptions
const availablePermissions = {
  'USER_MANAGEMENT': {
    name: 'User Management',
    description: 'Create, edit, delete, and manage user accounts',
    category: 'Administration'
  },
  'ROLE_MANAGEMENT': {
    name: 'Role Management',
    description: 'Create, edit, delete, and manage user roles and permissions',
    category: 'Administration'
  },
  'PATIENT_MANAGEMENT': {
    name: 'Patient Management',
    description: 'Access and manage patient records and information',
    category: 'Clinical'
  },
  'APPOINTMENT_MANAGEMENT': {
    name: 'Appointment Management',
    description: 'Schedule, modify, and manage patient appointments',
    category: 'Clinical'
  },
  'BILLING_MANAGEMENT': {
    name: 'Billing Management',
    description: 'Manage billing, invoices, and financial records',
    category: 'Financial'
  },
  'REPORT_GENERATION': {
    name: 'Report Generation',
    description: 'Generate and access various system reports',
    category: 'Analytics'
  },
  'FACILITY_MANAGEMENT': {
    name: 'Facility Management',
    description: 'Manage hospital facilities, rooms, and equipment',
    category: 'Operations'
  },
  'LABORATORY_MANAGEMENT': {
    name: 'Laboratory Management',
    description: 'Manage lab tests, results, and laboratory operations',
    category: 'Clinical'
  },
  'PHARMACY_MANAGEMENT': {
    name: 'Pharmacy Management',
    description: 'Manage medications, prescriptions, and pharmacy inventory',
    category: 'Clinical'
  },
  'SYSTEM_ADMINISTRATION': {
    name: 'System Administration',
    description: 'Full system administration and configuration access',
    category: 'Administration'
  },
  'AUDIT_LOG_ACCESS': {
    name: 'Audit Log Access',
    description: 'View and manage system audit logs and activity tracking',
    category: 'Administration'
  },
  'BACKUP_RESTORE': {
    name: 'Backup & Restore',
    description: 'Perform system backups and data restoration',
    category: 'Administration'
  },
  'NOTIFICATION_MANAGEMENT': {
    name: 'Notification Management',
    description: 'Manage system notifications and alerts',
    category: 'Operations'
  }
};

// @desc    Get all users (Admin only)
// @route   GET /api/admin/users
// @access  Private/Admin
export const getAllUsers = async (req, res) => {
  try {
    const models = getModels();
    if (!models.User) {
      return res.status(500).json({
        success: false,
        error: 'User model not available'
      });
    }

    const { page = 1, limit = 20, search, role, status, department } = req.query;

    // Build query
    let query = {};

    if (search) {
      const searchRegex = new RegExp(search, 'i');
      query.$or = [
        { firstName: searchRegex },
        { lastName: searchRegex },
        { email: searchRegex },
        { employeeId: searchRegex }
      ];
    }

    if (status && status !== 'all') {
      query.isActive = status === 'Active';
    }

    if (department && department !== 'all') {
      query.department = department;
    }

    // Build aggregation pipeline
    const pipeline = [
      { $match: query },
      {
        $lookup: {
          from: 'roles',
          localField: 'role',
          foreignField: '_id',
          as: 'roleInfo'
        }
      },
      {
        $unwind: {
          path: '$roleInfo',
          preserveNullAndEmptyArrays: true
        }
      }
    ];

    // Add role filter if specified
    if (role && role !== 'all') {
      pipeline.push({
        $match: { 'roleInfo._id': new mongoose.Types.ObjectId(role) }
      });
    }

    // Get total count for pagination
    const totalCountPipeline = [...pipeline];
    totalCountPipeline.push({ $count: 'total' });
    const totalResult = await models.User.aggregate(totalCountPipeline);
    const totalUsers = totalResult.length > 0 ? totalResult[0].total : 0;

    // Add pagination
    const skip = (page - 1) * limit;
    pipeline.push(
      { $sort: { createdAt: -1 } },
      { $skip: skip },
      { $limit: parseInt(limit) }
    );

    // Execute aggregation
    const users = await models.User.aggregate(pipeline);

    // Format user data
    const formattedUsers = users.map(user => ({
      _id: user._id,
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      role: user.roleInfo || { name: 'Unknown' },
      department: user.department,
      status: user.isActive ? 'Active' : 'Inactive',
      lastLogin: user.lastLogin,
      createdAt: user.createdAt,
      phone: user.phone,
      employeeId: user.employeeId,
      position: user.position
    }));

    res.status(200).json({
      success: true,
      data: formattedUsers,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: totalUsers,
        pages: Math.ceil(totalUsers / limit)
      }
    });
  } catch (error) {
    console.error('Get all users error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching users'
    });
  }
};

// @desc    Create new user (Admin only)
// @route   POST /api/admin/users
// @access  Private/Admin
export const createUser = async (req, res) => {
  try {
    const {
      firstName,
      lastName,
      email,
      password,
      role,
      department,
      phone,
      employeeId,
      position,
      specialization,
      licenseNumber
    } = req.body;

    // Validation
    if (!firstName || !lastName || !email || !password || !role) {
      return res.status(400).json({
        success: false,
        error: 'Required fields: firstName, lastName, email, password, role'
      });
    }

    // Check if user already exists
    const existingUser = mockUsers.find(user => user.email === email);
    if (existingUser) {
      return res.status(400).json({
        success: false,
        error: 'User with this email already exists'
      });
    }

    // Check if employeeId already exists
    if (employeeId) {
      const existingEmployeeId = mockUsers.find(user => user.employeeId === employeeId);
      if (existingEmployeeId) {
        return res.status(400).json({
          success: false,
          error: 'Employee ID already exists'
        });
      }
    }

    // Get role name
    const roleNames = {
      '1': 'Administrator',
      '2': 'Doctor',
      '3': 'Nurse',
      '4': 'Receptionist'
    };

    // Create new user
    const newUser = {
      _id: nextUserId.toString(),
      firstName,
      lastName,
      email,
      role: { _id: role, name: roleNames[role] || 'User' },
      department: department || 'General',
      status: 'Active',
      phone: phone || '',
      employeeId: employeeId || `EMP${String(nextUserId).padStart(3, '0')}`,
      position: position || '',
      specialization: specialization || '',
      licenseNumber: licenseNumber || '',
      lastLogin: null,
      createdAt: new Date()
    };

    // Add to mock database
    mockUsers.push(newUser);
    nextUserId++;

    // Remove password from response
    const userResponse = { ...newUser };
    delete userResponse.password;

    res.status(201).json({
      success: true,
      data: userResponse
    });
  } catch (error) {
    console.error('Create user error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while creating user'
    });
  }
};

// @desc    Update user (Admin only)
// @route   PUT /api/admin/users/:id
// @access  Private/Admin
export const updateUser = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    // Find user
    const userIndex = mockUsers.findIndex(user => user._id === id);
    if (userIndex === -1) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    // Remove password from update data (use separate endpoint)
    delete updateData.password;

    // Check if email is being changed and if it already exists
    if (updateData.email && updateData.email !== mockUsers[userIndex].email) {
      const existingUser = mockUsers.find(user => user.email === updateData.email && user._id !== id);
      if (existingUser) {
        return res.status(400).json({
          success: false,
          error: 'Email already exists'
        });
      }
    }

    // Check if employeeId is being changed and if it already exists
    if (updateData.employeeId && updateData.employeeId !== mockUsers[userIndex].employeeId) {
      const existingEmployeeId = mockUsers.find(user => user.employeeId === updateData.employeeId && user._id !== id);
      if (existingEmployeeId) {
        return res.status(400).json({
          success: false,
          error: 'Employee ID already exists'
        });
      }
    }

    // Update role name if role is being changed
    if (updateData.role) {
      const roleNames = {
        '1': 'Administrator',
        '2': 'Doctor',
        '3': 'Nurse',
        '4': 'Receptionist'
      };
      updateData.role = { _id: updateData.role, name: roleNames[updateData.role] || 'User' };
    }

    // Update user
    mockUsers[userIndex] = {
      ...mockUsers[userIndex],
      ...updateData,
      updatedAt: new Date()
    };

    res.status(200).json({
      success: true,
      data: mockUsers[userIndex]
    });
  } catch (error) {
    console.error('Update user error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while updating user'
    });
  }
};

// @desc    Delete user (Admin only)
// @route   DELETE /api/admin/users/:id
// @access  Private/Admin
export const deleteUser = async (req, res) => {
  try {
    const { id } = req.params;

    // Don't allow deletion of own account
    if (id === req.user?.id) {
      return res.status(400).json({
        success: false,
        error: 'Cannot delete your own account'
      });
    }

    // Find user
    const userIndex = mockUsers.findIndex(user => user._id === id);
    if (userIndex === -1) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    // Don't allow deletion of the last administrator
    const user = mockUsers[userIndex];
    if (user.role.name === 'Administrator') {
      const adminCount = mockUsers.filter(u => u.role.name === 'Administrator').length;
      if (adminCount <= 1) {
        return res.status(400).json({
          success: false,
          error: 'Cannot delete the last administrator'
        });
      }
    }

    // Remove user from mock database
    mockUsers.splice(userIndex, 1);

    res.status(200).json({
      success: true,
      message: 'User deleted successfully'
    });
  } catch (error) {
    console.error('Delete user error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while deleting user'
    });
  }
};

// @desc    Reset user password (Admin only)
// @route   PUT /api/admin/users/:id/reset-password
// @access  Private/Admin
export const resetUserPassword = async (req, res) => {
  try {
    res.status(200).json({
      success: true,
      message: 'Password reset successfully'
    });
  } catch (error) {
    console.error('Reset password error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while resetting password'
    });
  }
};

// @desc    Toggle user status (Admin only)
// @route   PUT /api/admin/users/:id/toggle-status
// @access  Private/Admin
export const toggleUserStatus = async (req, res) => {
  try {
    const { id } = req.params;

    // Don't allow deactivation of own account
    if (id === req.user?.id) {
      return res.status(400).json({
        success: false,
        error: 'Cannot deactivate your own account'
      });
    }

    // Find user
    const userIndex = mockUsers.findIndex(user => user._id === id);
    if (userIndex === -1) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    const user = mockUsers[userIndex];

    // Don't allow deactivation of the last active administrator
    if (user.role.name === 'Administrator' && user.status === 'Active') {
      const activeAdminCount = mockUsers.filter(u =>
        u.role.name === 'Administrator' && u.status === 'Active'
      ).length;
      if (activeAdminCount <= 1) {
        return res.status(400).json({
          success: false,
          error: 'Cannot deactivate the last active administrator'
        });
      }
    }

    // Toggle status
    const newStatus = user.status === 'Active' ? 'Inactive' : 'Active';
    mockUsers[userIndex].status = newStatus;
    mockUsers[userIndex].updatedAt = new Date();

    res.status(200).json({
      success: true,
      data: mockUsers[userIndex]
    });
  } catch (error) {
    console.error('Toggle user status error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while updating user status'
    });
  }
};

// @desc    Get user statistics (Admin only)
// @route   GET /api/admin/users/stats
// @access  Private/Admin
// @desc    Get all roles
// @route   GET /api/admin/roles
// @access  Private/Admin
export const getAllRoles = async (req, res) => {
  try {
    const models = getModels();

    // Ensure default roles exist
    await createDefaultRoles();

    // Try to get roles from database
    try {
      const roles = await models.Role.find({})
        .sort({ name: 1 })
        .lean();

      // Add user count for each role
      const rolesWithCount = await Promise.all(
        roles.map(async (role) => {
          let userCount = 0;
          try {
            if (models.User) {
              userCount = await models.User.countDocuments({ role: role._id });
            }
          } catch (err) {
            console.warn('Could not count users for role:', err.message);
          }

          return {
            ...role,
            userCount
          };
        })
      );

      return res.status(200).json({
        success: true,
        data: rolesWithCount
      });
    } catch (dbError) {
      console.warn('Database error, using fallback roles:', dbError.message);

      // Return default roles as fallback
      const defaultRoles = [
        {
          _id: '674a1b2c3d4e5f6789012345',
          name: 'Administrator',
          description: 'Full system access with all administrative privileges',
          permissions: [
            'USER_MANAGEMENT',
            'ROLE_MANAGEMENT',
            'PATIENT_MANAGEMENT',
            'APPOINTMENT_MANAGEMENT',
            'BILLING_MANAGEMENT',
            'REPORT_GENERATION',
            'FACILITY_MANAGEMENT',
            'LABORATORY_MANAGEMENT',
            'PHARMACY_MANAGEMENT',
            'SYSTEM_ADMINISTRATION'
          ],
          isSystemRole: true,
          isActive: true,
          userCount: 0,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          _id: '674a1b2c3d4e5f6789012346',
          name: 'Doctor',
          description: 'Medical staff with patient care and medical record access',
          permissions: [
            'PATIENT_MANAGEMENT',
            'APPOINTMENT_MANAGEMENT',
            'LABORATORY_MANAGEMENT',
            'PHARMACY_MANAGEMENT'
          ],
          isSystemRole: true,
          isActive: true,
          userCount: 0,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          _id: '674a1b2c3d4e5f6789012347',
          name: 'Nurse',
          description: 'Nursing staff with patient care and medication management',
          permissions: [
            'PATIENT_MANAGEMENT',
            'PHARMACY_MANAGEMENT',
            'LABORATORY_MANAGEMENT'
          ],
          isSystemRole: true,
          isActive: true,
          userCount: 0,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          _id: '674a1b2c3d4e5f6789012348',
          name: 'Receptionist',
          description: 'Front desk staff with appointment and basic patient management',
          permissions: [
            'APPOINTMENT_MANAGEMENT',
            'PATIENT_MANAGEMENT'
          ],
          isSystemRole: true,
          isActive: true,
          userCount: 0,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ];

      return res.status(200).json({
        success: true,
        data: defaultRoles
      });
    }
  } catch (error) {
    console.error('Get all roles error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching roles'
    });
  }
};

export const getUserStats = async (req, res) => {
  try {
    const models = getModels();
    if (!models.User) {
      return res.status(500).json({
        success: false,
        error: 'User model not available'
      });
    }

    // Get basic user counts
    const totalUsers = await models.User.countDocuments({});
    const activeUsers = await models.User.countDocuments({ isActive: true });
    const inactiveUsers = await models.User.countDocuments({ isActive: false });

    // Get users by role
    const usersByRole = await models.User.aggregate([
      {
        $lookup: {
          from: 'roles',
          localField: 'role',
          foreignField: '_id',
          as: 'roleInfo'
        }
      },
      {
        $unwind: {
          path: '$roleInfo',
          preserveNullAndEmptyArrays: true
        }
      },
      {
        $group: {
          _id: '$roleInfo.name',
          count: { $sum: 1 }
        }
      },
      {
        $sort: { _id: 1 }
      }
    ]);

    // Get recent logins (last 7 days)
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

    const recentLogins = await models.User.countDocuments({
      lastLogin: { $gte: sevenDaysAgo }
    });

    res.status(200).json({
      success: true,
      data: {
        totalUsers,
        activeUsers,
        inactiveUsers,
        usersByRole,
        recentLogins
      }
    });
  } catch (error) {
    console.error('Get user stats error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching user statistics'
    });
  }
};



// @desc    Create new role (Admin only)
// @route   POST /api/admin/roles
// @access  Private/Admin
export const createRole = async (req, res) => {
  try {
    const { name, description, permissions } = req.body;

    // Validation
    if (!name || !description) {
      return res.status(400).json({
        success: false,
        error: 'Name and description are required'
      });
    }

    if (!permissions || !Array.isArray(permissions) || permissions.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'At least one permission is required'
      });
    }

    // Check if role already exists
    const existingRole = mockRoles.find(role =>
      role.name.toLowerCase() === name.toLowerCase()
    );
    if (existingRole) {
      return res.status(400).json({
        success: false,
        error: 'Role with this name already exists'
      });
    }

    // Validate permissions
    const validPermissions = [
      'USER_MANAGEMENT',
      'ROLE_MANAGEMENT',
      'PATIENT_MANAGEMENT',
      'APPOINTMENT_MANAGEMENT',
      'BILLING_MANAGEMENT',
      'REPORT_GENERATION',
      'FACILITY_MANAGEMENT',
      'LABORATORY_MANAGEMENT',
      'PHARMACY_MANAGEMENT',
      'SYSTEM_ADMINISTRATION',
      'AUDIT_LOG_ACCESS',
      'BACKUP_RESTORE',
      'NOTIFICATION_MANAGEMENT'
    ];

    const invalidPermissions = permissions.filter(p => !validPermissions.includes(p));
    if (invalidPermissions.length > 0) {
      return res.status(400).json({
        success: false,
        error: `Invalid permissions: ${invalidPermissions.join(', ')}`
      });
    }

    const newRole = {
      _id: nextRoleId.toString(),
      name: name.trim(),
      description: description.trim(),
      permissions,
      createdAt: new Date(),
      isSystemRole: false
    };

    // Add to mock database
    mockRoles.push(newRole);
    nextRoleId++;

    res.status(201).json({
      success: true,
      data: newRole
    });
  } catch (error) {
    console.error('Create role error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while creating role'
    });
  }
};

// @desc    Update role (Admin only)
// @route   PUT /api/admin/roles/:id
// @access  Private/Admin
export const updateRole = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, permissions } = req.body;

    // Find role
    const roleIndex = mockRoles.findIndex(role => role._id === id);
    if (roleIndex === -1) {
      return res.status(404).json({
        success: false,
        error: 'Role not found'
      });
    }

    const role = mockRoles[roleIndex];

    // Don't allow modification of system roles' core properties
    if (role.isSystemRole && (name !== role.name)) {
      return res.status(400).json({
        success: false,
        error: 'Cannot modify system role name'
      });
    }

    // Validation
    if (name && name.trim() !== role.name) {
      const existingRole = mockRoles.find(r =>
        r.name.toLowerCase() === name.toLowerCase() && r._id !== id
      );
      if (existingRole) {
        return res.status(400).json({
          success: false,
          error: 'Role with this name already exists'
        });
      }
    }

    if (permissions && Array.isArray(permissions)) {
      const validPermissions = [
        'USER_MANAGEMENT',
        'ROLE_MANAGEMENT',
        'PATIENT_MANAGEMENT',
        'APPOINTMENT_MANAGEMENT',
        'BILLING_MANAGEMENT',
        'REPORT_GENERATION',
        'FACILITY_MANAGEMENT',
        'LABORATORY_MANAGEMENT',
        'PHARMACY_MANAGEMENT',
        'SYSTEM_ADMINISTRATION',
        'AUDIT_LOG_ACCESS',
        'BACKUP_RESTORE',
        'NOTIFICATION_MANAGEMENT'
      ];

      const invalidPermissions = permissions.filter(p => !validPermissions.includes(p));
      if (invalidPermissions.length > 0) {
        return res.status(400).json({
          success: false,
          error: `Invalid permissions: ${invalidPermissions.join(', ')}`
        });
      }
    }

    // Update role
    if (name) mockRoles[roleIndex].name = name.trim();
    if (description) mockRoles[roleIndex].description = description.trim();
    if (permissions) mockRoles[roleIndex].permissions = permissions;
    mockRoles[roleIndex].updatedAt = new Date();

    res.status(200).json({
      success: true,
      data: mockRoles[roleIndex]
    });
  } catch (error) {
    console.error('Update role error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while updating role'
    });
  }
};

// @desc    Delete role (Admin only)
// @route   DELETE /api/admin/roles/:id
// @access  Private/Admin
export const deleteRole = async (req, res) => {
  try {
    const { id } = req.params;

    // Find role
    const roleIndex = mockRoles.findIndex(role => role._id === id);
    if (roleIndex === -1) {
      return res.status(404).json({
        success: false,
        error: 'Role not found'
      });
    }

    const role = mockRoles[roleIndex];

    // Don't allow deletion of system roles
    if (role.isSystemRole) {
      return res.status(400).json({
        success: false,
        error: 'Cannot delete system roles'
      });
    }

    // Check if any users have this role
    const usersWithRole = mockUsers.filter(user => user.role._id === id);
    if (usersWithRole.length > 0) {
      return res.status(400).json({
        success: false,
        error: `Cannot delete role. ${usersWithRole.length} users are assigned to this role.`
      });
    }

    // Remove role from mock database
    mockRoles.splice(roleIndex, 1);

    res.status(200).json({
      success: true,
      message: 'Role deleted successfully'
    });
  } catch (error) {
    console.error('Delete role error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while deleting role'
    });
  }
};

// @desc    Get audit logs (Admin only)
// @route   GET /api/admin/audit-logs
// @access  Private/Admin
export const getAuditLogs = async (req, res) => {
  try {
    const models = getModels();
    if (!models.AuditLog) {
      // Return mock audit logs if model is not available
      const mockAuditLogs = [
        {
          _id: '1',
          user: { firstName: 'System', lastName: 'Admin', email: '<EMAIL>' },
          action: 'User Login',
          timestamp: new Date(),
          ipAddress: '127.0.0.1',
          status: 'Success',
          details: 'System initialization login'
        },
        {
          _id: '2',
          user: { firstName: 'System', lastName: 'User', email: '<EMAIL>' },
          action: 'Database Connection',
          timestamp: new Date(),
          ipAddress: '127.0.0.1',
          status: 'Success',
          details: 'Database connection established'
        }
      ];

      return res.status(200).json({
        success: true,
        data: mockAuditLogs,
        pagination: {
          page: 1,
          limit: 50,
          total: mockAuditLogs.length,
          pages: 1
        }
      });
    }

    const { page = 1, limit = 50, action, user, status, days = 30 } = req.query;

    // Build query
    let query = {};

    if (action) {
      query.action = new RegExp(action, 'i');
    }

    if (user) {
      query.user = user;
    }

    if (status) {
      query.status = status;
    }

    // Filter by date range
    if (days) {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - parseInt(days));
      query.timestamp = { $gte: startDate };
    }

    // Get total count
    const totalLogs = await models.AuditLog.countDocuments(query);

    // Get paginated results
    const skip = (page - 1) * limit;
    const auditLogs = await models.AuditLog.find(query)
      .populate('user', 'firstName lastName email')
      .sort({ timestamp: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    res.status(200).json({
      success: true,
      data: auditLogs,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: totalLogs,
        pages: Math.ceil(totalLogs / limit)
      }
    });
  } catch (error) {
    console.error('Get audit logs error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching audit logs'
    });
  }
};

// @desc    Create audit log entry
// @route   POST /api/admin/audit-logs
// @access  Private
export const createAuditLog = async (req, res) => {
  try {
    const { action, resource, resourceId, details, ipAddress } = req.body;

    const newAuditLog = {
      _id: Date.now().toString(),
      user: req.user?.id || 'system',
      action,
      resource,
      resourceId,
      details,
      ipAddress: ipAddress || req.ip,
      timestamp: new Date()
    };

    res.status(201).json({
      success: true,
      data: newAuditLog
    });
  } catch (error) {
    console.error('Create audit log error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while creating audit log'
    });
  }
};

// @desc    Get system statistics (Admin only)
// @route   GET /api/admin/system-stats
// @access  Private/Admin
export const getSystemStats = async (req, res) => {
  try {
    const mockSystemStats = {
      totalUsers: 25,
      totalRoles: 4,
      activeUsers: 22,
      recentActivity: 45,
      recentLogins: 18,
      systemUptime: process.uptime(),
      timestamp: new Date()
    };

    res.status(200).json({
      success: true,
      data: mockSystemStats
    });
  } catch (error) {
    console.error('Get system stats error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching system statistics'
    });
  }
};

// ===== PERMISSION MANAGEMENT =====

// @desc    Get all available permissions
// @route   GET /api/admin/permissions
// @access  Private/Admin
export const getAvailablePermissions = async (req, res) => {
  try {
    // Group permissions by category
    const permissionsByCategory = {};

    Object.entries(availablePermissions).forEach(([key, permission]) => {
      const category = permission.category;
      if (!permissionsByCategory[category]) {
        permissionsByCategory[category] = [];
      }
      permissionsByCategory[category].push({
        key,
        ...permission
      });
    });

    res.status(200).json({
      success: true,
      data: {
        permissions: availablePermissions,
        permissionsByCategory
      }
    });
  } catch (error) {
    console.error('Get available permissions error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching permissions'
    });
  }
};

// @desc    Get user permissions
// @route   GET /api/admin/users/:id/permissions
// @access  Private/Admin
export const getUserPermissions = async (req, res) => {
  try {
    const { id } = req.params;

    // Find user
    const user = mockUsers.find(user => user._id === id);
    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    // Find user's role
    const role = mockRoles.find(role => role._id === user.role._id);
    if (!role) {
      return res.status(404).json({
        success: false,
        error: 'User role not found'
      });
    }

    // Get detailed permissions
    const userPermissions = role.permissions.map(permissionKey => ({
      key: permissionKey,
      ...availablePermissions[permissionKey]
    }));

    res.status(200).json({
      success: true,
      data: {
        user: {
          _id: user._id,
          firstName: user.firstName,
          lastName: user.lastName,
          email: user.email,
          role: user.role
        },
        permissions: userPermissions,
        permissionKeys: role.permissions
      }
    });
  } catch (error) {
    console.error('Get user permissions error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching user permissions'
    });
  }
};

// @desc    Check if user has specific permission
// @route   GET /api/admin/users/:id/permissions/:permission
// @access  Private/Admin
export const checkUserPermission = async (req, res) => {
  try {
    const { id, permission } = req.params;

    // Find user
    const user = mockUsers.find(user => user._id === id);
    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    // Find user's role
    const role = mockRoles.find(role => role._id === user.role._id);
    if (!role) {
      return res.status(404).json({
        success: false,
        error: 'User role not found'
      });
    }

    // Check if permission exists
    if (!availablePermissions[permission]) {
      return res.status(400).json({
        success: false,
        error: 'Invalid permission'
      });
    }

    // Check if user has permission
    const hasPermission = role.permissions.includes(permission);

    res.status(200).json({
      success: true,
      data: {
        user: {
          _id: user._id,
          firstName: user.firstName,
          lastName: user.lastName,
          email: user.email,
          role: user.role
        },
        permission: {
          key: permission,
          ...availablePermissions[permission]
        },
        hasPermission
      }
    });
  } catch (error) {
    console.error('Check user permission error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while checking user permission'
    });
  }
};
