import mongoose from 'mongoose';

const auditLogSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  action: {
    type: String,
    required: true,
    enum: [
      'CREATE',
      'READ',
      'UPDATE',
      'DELETE',
      'LOGIN',
      'LOGOUT',
      'PASSWORD_CHANGE',
      'ROLE_CHANGE',
      'STATUS_CHANGE',
      'EXPORT',
      'IMPORT',
      'BACKUP',
      'RESTORE',
      'SYSTEM_CONFIG',
      'USER_MANAGEMENT',
      'PATIENT_ACCESS',
      'APPOINTMENT_MANAGEMENT',
      'BILLING_ACCESS',
      'REPORT_GENERATION'
    ]
  },
  resource: {
    type: String,
    required: true,
    enum: [
      'User',
      'Patient',
      'Appointment',
      'Bill',
      'Role',
      'System',
      'Report',
      'Notification',
      'Equipment',
      'Room',
      'Medication',
      'LabTest'
    ]
  },
  resourceId: {
    type: String,
    default: null
  },
  details: {
    type: mongoose.Schema.Types.Mixed,
    default: {}
  },
  ipAddress: {
    type: String,
    required: true
  },
  userAgent: {
    type: String,
    default: null
  },
  timestamp: {
    type: Date,
    default: Date.now,
    required: true
  },
  severity: {
    type: String,
    enum: ['LOW', 'MEDIUM', 'HIGH', 'CRITICAL'],
    default: 'LOW'
  },
  status: {
    type: String,
    enum: ['SUCCESS', 'FAILED', 'WARNING'],
    default: 'SUCCESS'
  }
}, {
  timestamps: true
});

// Indexes for better performance
auditLogSchema.index({ user: 1, timestamp: -1 });
auditLogSchema.index({ action: 1, timestamp: -1 });
auditLogSchema.index({ resource: 1, timestamp: -1 });
auditLogSchema.index({ timestamp: -1 });
auditLogSchema.index({ severity: 1, timestamp: -1 });

// Virtual for formatted timestamp
auditLogSchema.virtual('formattedTimestamp').get(function() {
  return this.timestamp.toLocaleString();
});

// Static method to create audit log
auditLogSchema.statics.createLog = async function(data) {
  try {
    const log = new this(data);
    await log.save();
    await log.populate('user', 'firstName lastName email');
    return log;
  } catch (error) {
    console.error('Error creating audit log:', error);
    throw error;
  }
};

// Static method to get recent activity
auditLogSchema.statics.getRecentActivity = async function(hours = 24, limit = 100) {
  const startTime = new Date();
  startTime.setHours(startTime.getHours() - hours);

  return this.find({
    timestamp: { $gte: startTime }
  })
  .populate('user', 'firstName lastName email')
  .sort({ timestamp: -1 })
  .limit(limit);
};

// Static method to get user activity
auditLogSchema.statics.getUserActivity = async function(userId, days = 30) {
  const startTime = new Date();
  startTime.setDate(startTime.getDate() - days);

  return this.find({
    user: userId,
    timestamp: { $gte: startTime }
  })
  .sort({ timestamp: -1 });
};

// Static method to get activity by resource
auditLogSchema.statics.getResourceActivity = async function(resource, resourceId = null, days = 30) {
  const startTime = new Date();
  startTime.setDate(startTime.getDate() - days);

  const query = {
    resource,
    timestamp: { $gte: startTime }
  };

  if (resourceId) {
    query.resourceId = resourceId;
  }

  return this.find(query)
    .populate('user', 'firstName lastName email')
    .sort({ timestamp: -1 });
};

// Static method to get security events
auditLogSchema.statics.getSecurityEvents = async function(days = 7) {
  const startTime = new Date();
  startTime.setDate(startTime.getDate() - days);

  return this.find({
    $or: [
      { action: 'LOGIN' },
      { action: 'LOGOUT' },
      { action: 'PASSWORD_CHANGE' },
      { action: 'ROLE_CHANGE' },
      { action: 'STATUS_CHANGE' },
      { severity: 'HIGH' },
      { severity: 'CRITICAL' },
      { status: 'FAILED' }
    ],
    timestamp: { $gte: startTime }
  })
  .populate('user', 'firstName lastName email')
  .sort({ timestamp: -1 });
};

// Middleware to automatically set severity based on action
auditLogSchema.pre('save', function(next) {
  if (!this.severity || this.severity === 'LOW') {
    switch (this.action) {
      case 'DELETE':
      case 'ROLE_CHANGE':
      case 'STATUS_CHANGE':
        this.severity = 'HIGH';
        break;
      case 'PASSWORD_CHANGE':
      case 'USER_MANAGEMENT':
      case 'SYSTEM_CONFIG':
        this.severity = 'MEDIUM';
        break;
      case 'LOGIN':
      case 'LOGOUT':
      case 'READ':
        this.severity = 'LOW';
        break;
      default:
        this.severity = 'LOW';
    }
  }
  next();
});

const AuditLog = mongoose.model('AuditLog', auditLogSchema);

export default AuditLog;
