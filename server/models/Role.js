import mongoose from 'mongoose';

const RoleSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Role name is required'],
    unique: true,
    trim: true,
    maxlength: [50, 'Role name cannot exceed 50 characters']
  },
  description: {
    type: String,
    trim: true,
    maxlength: [200, 'Description cannot exceed 200 characters']
  },
  permissions: [{
    type: String,
    enum: [
      'USER_MANAGEMENT',
      'ROLE_MANAGEMENT', 
      'PATIENT_MANAGEMENT',
      'APPOINTMENT_MANAGEMENT',
      'BILLING_MANAGEMENT',
      'REPORT_GENERATION',
      'FACILITY_MANAGEMENT',
      'LABORATORY_MANAGEMENT',
      'PHARMACY_MANAGEMENT',
      'SYSTEM_ADMINISTRATION',
      'AUDIT_LOG_ACCESS',
      'BACKUP_RESTORE',
      'NOTIFICATION_MANAGEMENT'
    ]
  }],
  isSystemRole: {
    type: Boolean,
    default: false
  },
  isActive: {
    type: Boolean,
    default: true
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// Index for better query performance
RoleSchema.index({ name: 1 });
RoleSchema.index({ isActive: 1 });

// Virtual for user count
RoleSchema.virtual('userCount', {
  ref: 'User',
  localField: '_id',
  foreignField: 'role',
  count: true
});

// Ensure virtual fields are serialized
RoleSchema.set('toJSON', { virtuals: true });

// Pre-remove middleware to prevent deletion of system roles
RoleSchema.pre('remove', function(next) {
  if (this.isSystemRole) {
    return next(new Error('Cannot delete system roles'));
  }
  next();
});

const Role = mongoose.model('Role', RoleSchema);

export default Role;
